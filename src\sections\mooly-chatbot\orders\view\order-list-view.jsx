'use client';

import { useState, useCallback } from 'react';
import { varAlpha } from 'minimal-shared/utils';
import { useBoolean, useSetState } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Card from '@mui/material/Card';
import Tabs from '@mui/material/Tabs';
import Table from '@mui/material/Table';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import TableBody from '@mui/material/TableBody';
import Container from '@mui/material/Container';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import { fIsAfter } from 'src/utils/format-time';

import { DashboardContent } from 'src/layouts/dashboard';
import { useOrders, cancelOrder } from 'src/actions/mooly-chatbot/order-service';
import { ORDER_STATUS_OPTIONS } from 'src/actions/mooly-chatbot/order-constants';

import { Label } from 'src/components/label';
import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import {
  useTable,
  emptyRows,
  rowInPage,
  TableNoData,
  getComparator,
  TableEmptyRows,
  TableHeadCustom,
  TableSelectedAction,
  TablePaginationCustom,
} from 'src/components/table';

import { OrderTableRow } from '../order-table-row';
import OrderViewDialog from '../order-view-dialog';
import { OrderTableToolbar } from '../order-table-toolbar';
import { OrderCreateDialog } from '../order-create-dialog';
import { OrderTableFiltersResult } from '../order-table-filters-result';
import WorkflowSettingsDialog from '../workflow-settings-dialog';

// ----------------------------------------------------------------------

// Lấy tất cả trạng thái cho filter tabs
const STATUS_OPTIONS = [
  { value: 'all', label: 'Tất cả' },
  ...ORDER_STATUS_OPTIONS.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
];

const TABLE_HEAD = [
  { id: 'orderNumber', label: 'Mã đơn hàng', width: 120 },
  { id: 'customerName', label: 'Khách hàng', minWidth: 200 },
  { id: 'createdAt', label: 'Ngày tạo', width: 140 },
  { id: 'totalAmount', label: 'Tổng tiền', width: 140, align: 'center' },
  { id: 'status', label: 'Trạng thái', width: 110, align: 'center' },
  { id: '', width: 120, align: 'right' }, // Increase width for action column
];

// ----------------------------------------------------------------------

export default function OrderListView() {
  const table = useTable({ defaultOrderBy: 'createdAt', defaultOrder: 'desc' });
  const router = useRouter();

  const confirmDialog = useBoolean();
  const createDialog = useBoolean();
  const viewDialog = useBoolean();
  const workflowDialog = useBoolean();

  const [selectedOrderId] = useState(null);

  const filters = useSetState({
    name: '',
    status: 'all',
    startDate: null,
    endDate: null,
  });
  const { state: currentFilters, setState: updateFilters } = filters;

  const dateError = fIsAfter(currentFilters.startDate, currentFilters.endDate);

  // Lấy danh sách đơn hàng từ API
  const {
    orders = [],
    isLoading,
    mutate,
  } = useOrders({
    filters: {
      ...(currentFilters.status !== 'all' && {
        status: currentFilters.status,
      }),
      ...(currentFilters.name && {
        or: [
          { orderNumber: { contains: currentFilters.name, mode: 'insensitive' } },
          { customerName: { contains: currentFilters.name, mode: 'insensitive' } },
          { customerEmail: { contains: currentFilters.name, mode: 'insensitive' } },
          { customerPhone: { contains: currentFilters.name, mode: 'insensitive' } },
        ],
      }),
      ...(currentFilters.startDate &&
        currentFilters.endDate && {
          createdAt: {
            gte: currentFilters.startDate,
            lte: currentFilters.endDate,
          },
        }),
    },
  });

  const dataFiltered = applyFilter({
    inputData: orders,
    comparator: getComparator(table.order, table.orderBy),
  });

  const notFound = !dataFiltered.length && !isLoading;

  const canReset =
    !!currentFilters.name ||
    currentFilters.status !== 'all' ||
    (!!currentFilters.startDate && !!currentFilters.endDate);

  const handleFilterStatus = useCallback(
    (_, newValue) => {
      table.onResetPage();
      updateFilters({ status: newValue });
    },
    [table, updateFilters]
  );

  const handleCancelRow = useCallback(
    (id) => {
      const cancelRow = async () => {
        try {
          // Gọi API hủy đơn hàng
          const result = await cancelOrder(id, 'Hủy đơn hàng từ danh sách');

          if (result.success) {
            mutate();
            table.onUpdatePageDeleteRow(dataFiltered.length);
            toast.success('Hủy đơn hàng thành công!');
          } else {
            toast.error(result.error || 'Hủy đơn hàng thất bại!');
          }
        } catch (error) {
          console.error(error);
          toast.error('Hủy đơn hàng thất bại!');
        }
      };

      confirmDialog.onTrue();
      confirmDialog.onFalse(cancelRow);
    },
    [confirmDialog, dataFiltered.length, mutate, table]
  );

  const handleCancelRows = useCallback(() => {
    const cancelRows = async () => {
      try {
        // Gọi API hủy nhiều đơn hàng
        const results = await Promise.all(
          table.selected.map((id) => cancelOrder(id, 'Hủy hàng loạt từ danh sách'))
        );

        // Kiểm tra kết quả
        const failedCount = results.filter(result => !result.success).length;
        const successCount = results.length - failedCount;

        mutate();
        table.onUpdatePageDeleteRows({
          totalRows: dataFiltered.length,
          totalRowsInPage: rowInPage(table.page, table.rowsPerPage, dataFiltered.length),
          totalRowsFiltered: dataFiltered.length,
        });

        if (failedCount === 0) {
          toast.success(`Hủy thành công ${successCount} đơn hàng!`);
        } else {
          toast.warning(`Hủy thành công ${successCount}/${results.length} đơn hàng!`);
        }
      } catch (error) {
        console.error(error);
        toast.error('Hủy đơn hàng thất bại!');
      }
    };

    confirmDialog.onTrue();
    confirmDialog.onFalse(cancelRows);
  }, [confirmDialog, dataFiltered.length, mutate, table]);

  const handleViewRow = useCallback(
    (id) => {
      router.push(paths.dashboard.moolyChatbot.orders.details(id));
    },
    [router]
  );

  const handleEditRow = useCallback(
    (id) => {
      router.push(paths.dashboard.moolyChatbot.orders.details(id));
    },
    [router]
  );

  const handleCreateOrderSuccess = useCallback(() => {
    mutate();
    createDialog.onFalse();
    toast.success('Tạo đơn hàng thành công!');
  }, [createDialog, mutate]);

  return (
    <DashboardContent>
      <Container maxWidth="lg">
        <CustomBreadcrumbs
          heading="Quản lý đơn hàng"
          links={[
            { name: 'Dashboard', href: paths.dashboard.root },
            { name: 'Mooly Chatbot', href: paths.dashboard.moolyChatbot.root },
            { name: 'Đơn hàng' },
          ]}
          action={
            <Stack direction="row" spacing={1}>
              <Button
                variant="outlined"
                startIcon={<Iconify icon="solar:settings-bold" />}
                onClick={workflowDialog.onTrue}
              >
                Cài đặt quy trình
              </Button>
              <Button
                variant="contained"
                startIcon={<Iconify icon="mingcute:add-line" />}
                onClick={createDialog.onTrue}
              >
                Tạo đơn hàng
              </Button>
            </Stack>
          }
          sx={{ mb: { xs: 3, md: 5 } }}
        />

        <Card>
          <Tabs
            value={currentFilters.status}
            onChange={handleFilterStatus}
            sx={[
              (theme) => ({
                px: 2.5,
                boxShadow: `inset 0 -2px 0 0 ${varAlpha(theme.vars.palette.grey['500Channel'], 0.08)}`,
              }),
            ]}
          >
            {STATUS_OPTIONS.map((tab) => (
              <Tab
                key={tab.value}
                iconPosition="end"
                value={tab.value}
                label={tab.label}
                icon={
                  <Label
                    variant={
                      tab.value === 'all' || tab.value === currentFilters.status ? 'filled' : 'soft'
                    }
                    color={
                      tab.value === 'all' ? 'default' :
                      (tab.color || 'default')
                    }
                  >
                    {tab.value === 'all'
                      ? orders.length
                      : orders.filter((order) => order.status === tab.value).length}
                  </Label>
                }
              />
            ))}
          </Tabs>

          <OrderTableToolbar
            filters={filters}
            onResetPage={table.onResetPage}
            dateError={dateError}
          />

          {canReset && (
            <OrderTableFiltersResult
              filters={filters}
              totalResults={dataFiltered.length}
              onResetPage={table.onResetPage}
              sx={{ p: 2.5, pt: 0 }}
            />
          )}

          <Box sx={{ position: 'relative' }}>
            <TableSelectedAction
              dense={table.dense}
              numSelected={table.selected.length}
              rowCount={dataFiltered.length}
              onSelectAllRows={(checked) =>
                table.onSelectAllRows(
                  checked,
                  dataFiltered.map((row) => row.id)
                )
              }
              action={
                <Tooltip title="Hủy đơn hàng">
                  <IconButton color="error" onClick={confirmDialog.onTrue}>
                    <Iconify icon="solar:close-circle-bold" />
                  </IconButton>
                </Tooltip>
              }
            />

            <Scrollbar sx={{ minHeight: 444 }}>
              <Table
                size={table.dense ? 'small' : 'medium'}
                sx={{
                  minWidth: 960,
                  '& .MuiTableCell-root': {
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  },
                  '& .MuiTableCell-root:last-child': {
                    whiteSpace: 'nowrap',
                    minWidth: 120,
                  }
                }}
              >
                <TableHeadCustom
                  order={table.order}
                  orderBy={table.orderBy}
                  headCells={TABLE_HEAD}
                  rowCount={dataFiltered.length}
                  numSelected={table.selected.length}
                  onSort={table.onSort}
                  onSelectAllRows={(checked) =>
                    table.onSelectAllRows(
                      checked,
                      dataFiltered.map((row) => row.id)
                    )
                  }
                />

                <TableBody>
                  {dataFiltered
                    .slice(
                      table.page * table.rowsPerPage,
                      table.page * table.rowsPerPage + table.rowsPerPage
                    )
                    .map((row) => (
                      <OrderTableRow
                        key={row.id}
                        row={row}
                        selected={table.selected.includes(row.id)}
                        onSelectRow={() => table.onSelectRow(row.id)}
                        onDeleteRow={() => handleCancelRow(row.id)}
                        onViewRow={() => handleViewRow(row.id)}
                        onEditRow={() => handleEditRow(row.id)}
                      />
                    ))}

                  <TableEmptyRows
                    height={table.dense ? 56 : 76}
                    emptyRows={emptyRows(table.page, table.rowsPerPage, dataFiltered.length)}
                  />

                  <TableNoData notFound={notFound} />
                </TableBody>
              </Table>
            </Scrollbar>
          </Box>

          <TablePaginationCustom
            count={dataFiltered.length}
            page={table.page}
            rowsPerPage={table.rowsPerPage}
            onPageChange={table.onChangePage}
            onRowsPerPageChange={table.onChangeRowsPerPage}
            dense={table.dense}
            onChangeDense={table.onChangeDense}
          />
        </Card>
      </Container>

      <OrderCreateDialog
        open={createDialog.value}
        onClose={createDialog.onFalse}
        onSuccess={handleCreateOrderSuccess}
      />

      <OrderViewDialog
        open={viewDialog.value}
        onClose={viewDialog.onFalse}
        orderId={selectedOrderId}
        onEdit={(id) => router.push(paths.dashboard.moolyChatbot.orders.details(id))}
      />

      <ConfirmDialog
        open={confirmDialog.value}
        onClose={confirmDialog.onFalse}
        title="Hủy đơn hàng"
        content={
          <>
            Bạn có chắc chắn muốn hủy <strong>{table.selected.length}</strong> đơn hàng này?
            <br />
            <small style={{ color: '#666', marginTop: '8px', display: 'block' }}>
              Đơn hàng sẽ được chuyển sang trạng thái &quot;Đã hủy&quot; và tồn kho sẽ được hoàn lại.
            </small>
          </>
        }
        action={
          <Button
            variant="contained"
            color="error"
            onClick={() => {
              handleCancelRows();
              confirmDialog.onFalse();
            }}
          >
            Hủy đơn hàng
          </Button>
        }
      />

      {/* Workflow Settings Dialog */}
      <WorkflowSettingsDialog
        open={workflowDialog.value}
        onClose={workflowDialog.onFalse}
      />
    </DashboardContent>
  );
}

// ----------------------------------------------------------------------

function applyFilter({ inputData, comparator }) {
  if (!inputData.length) return [];

  const stabilizedThis = inputData.map((el, index) => [el, index]);

  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  return stabilizedThis.map((el) => el[0]);
}
